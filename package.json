{"name": "dunshan<PERSON>-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@vueuse/core": "^13.5.0", "axios": "^1.10.0", "nprogress": "^0.2.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@iconify-json/ci": "^1.2.2", "@iconify-json/lucide": "^1.2.58", "@iconify-json/material-symbols": "^1.2.30", "@iconify-json/material-symbols-light": "^1.2.30", "@iconify-json/mdi": "^1.2.3", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/ph": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@iconify-json/streamline": "^1.2.5", "@iconify-json/tabler": "^1.2.20", "@iconify-json/uil": "^1.2.3", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@types/nprogress": "^0.2.3", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "pinia-plugin-persistedstate": "^4.4.1", "prettier": "3.5.3", "typescript": "~5.8.0", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}