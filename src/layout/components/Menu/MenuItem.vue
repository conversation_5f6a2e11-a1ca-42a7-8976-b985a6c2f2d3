<script lang="ts" setup>
// 定义组件属性
const props = defineProps({
  menu: {
    type: Object as PropType<any>,
    required: true
  }
})
// 解构菜单数据
const { menu } = toRefs(props)

// 过滤掉 hidden: true 的子菜单
const visibleChildren = computed(() => {
  return menu.value.children?.filter((child: any) => !child.meta?.hidden) || []
})
</script>

<template>
  <!-- 无子菜单或子菜单为空时，直接渲染菜单项 -->
  <template v-if="!menu.children || visibleChildren.length === 0">
    <a-menu-item :key="menu.path">
      <template #icon v-if="menu?.icon">
        <component :is="menu?.icon"></component>
      </template>
      {{ menu.title }}
    </a-menu-item>
  </template>

  <!-- 只有一个子菜单时，直接展示子菜单项而不显示父级 -->
  <template v-else-if="visibleChildren.length === 1">
    <a-menu-item :key="visibleChildren[0].path">
      <template #icon v-if="visibleChildren[0]?.icon || menu?.icon">
        <component :is="visibleChildren[0]?.icon || menu?.icon"></component>
      </template>
      {{ visibleChildren[0].title }}
    </a-menu-item>
  </template>

  <!-- 多个子菜单时，渲染为可展开的子菜单 -->
  <a-sub-menu v-else :key="menu.path" :title="menu.title">
    <template #icon v-if="menu?.icon">
      <component :is="menu?.icon"></component>
    </template>
    <MenuItem
      v-for="menuChild of visibleChildren"
      :key="menuChild.path"
      :menu="menuChild"
    />
  </a-sub-menu>
</template>

<style scoped></style>
