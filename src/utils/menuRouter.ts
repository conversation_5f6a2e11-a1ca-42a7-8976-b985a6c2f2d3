import type { RouteRecordRaw } from 'vue-router'

/**
 * @description 菜单路由数组 format
 * @param { Array } router 路由数组
 * @param { String } parentPath 父级路由 path
 * @return { Array }
 */
export const menuRouterFormat = (
  router: RouteRecordRaw[],
  parentPath?: string
): RouteRecordRaw[] => {
  return router.map(item => {
    // 处理路径拼接，避免出现多个斜杠
    if (!item.path.startsWith('/')) {
      // 子路由：拼接父路径
      if (parentPath) {
        // 确保父路径不以斜杠结尾，避免重复斜杠
        const cleanParentPath = parentPath.endsWith('/')
          ? parentPath.slice(0, -1)
          : parentPath
        item.path = `${cleanParentPath}/${item.path}`
      } else {
        item.path = `/${item.path}`
      }
    } else if (parentPath && item.path === '/') {
      // 特殊情况：父路径存在且当前路径为根路径
      item.path = parentPath
    }
    // 如果路径已经以 / 开头且不是根路径，保持不变

    // 存在 children 属性，且 children 数组长度大于 0，开始递归
    if (item.children && item.children.length > 0) {
      item.children = menuRouterFormat(item.children, item.path)
    }

    return Object.assign({}, item, item.meta || {})
  })
}
