<script setup lang="ts">
import { createHostApi } from '@/api/host'
import type { HostRecord } from '@/types/host'
import HostForm from './HostForm.vue'

/**
 * 组件属性定义
 */
interface Props {
  /** 弹窗显示状态 */
  visible: boolean
}

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 显示状态变化事件 - 使用v-model:visible双向绑定 */
  'update:visible': [visible: boolean]
  /** 新增成功事件 - 通知父组件刷新数据 */
  success: []
}>()

// 组件属性
const props = defineProps<Props>()

// 表单组件引用
const hostFormRef = ref()
// 提交加载状态
const submitLoading = ref(false)

// 弹窗显示状态的双向绑定计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

/**
 * 处理表单提交
 * 调用API创建主机，成功后关闭弹窗并通知父组件
 */
const handleSubmit = async (formData: HostRecord) => {
  try {
    submitLoading.value = true

    // 调用创建主机API
    const response = await createHostApi(formData)

    if (response.code === 200) {
      AMessage.success('主机创建成功')

      // 关闭弹窗
      modalVisible.value = false

      // 重置表单
      hostFormRef.value?.resetForm()

      // 通知父组件刷新数据
      emit('success')
    } else {
      AMessage.error(response.message || '主机创建失败')
    }
  } catch (error) {
    console.error('创建主机失败:', error)
    AMessage.error('主机创建失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

/**
 * 处理取消操作
 * 关闭弹窗并重置表单
 */
const handleCancel = () => {
  modalVisible.value = false
  hostFormRef.value?.resetForm()
}

/**
 * 弹窗关闭后的回调
 * 确保表单状态重置
 */
const handleAfterClose = () => {
  hostFormRef.value?.resetForm()
  submitLoading.value = false
}
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="新增主机"
    width="600px"
    :mask-closable="false"
    :esc-to-close="false"
    :footer="false"
    @after-close="handleAfterClose"
  >
    <div v-loading="submitLoading">
      <HostForm
        ref="hostFormRef"
        mode="add"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </div>
  </a-modal>
</template>

<style scoped></style>
