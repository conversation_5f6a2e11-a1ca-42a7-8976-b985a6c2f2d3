<script setup lang="ts">
import { updateHostApi } from '@/api/host'
import type { HostRecord } from '@/types/host'
import HostForm from './HostForm.vue'

/**
 * 组件属性定义
 */
interface Props {
  /** 弹窗显示状态 */
  visible: boolean
  /** 要编辑的主机数据 */
  hostData?: HostRecord
}

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 显示状态变化事件 - 使用v-model:visible双向绑定 */
  'update:visible': [visible: boolean]
  /** 编辑成功事件 - 通知父组件刷新数据 */
  success: []
}>()

// 组件属性
const props = defineProps<Props>()

// 表单组件引用
const hostFormRef = ref()
// 提交加载状态
const submitLoading = ref(false)

// 弹窗显示状态的双向绑定计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

/**
 * 处理表单提交
 * 调用API更新主机，成功后关闭弹窗并通知父组件
 */
const handleSubmit = async (formData: HostRecord) => {
  if (!props.hostData?.id) {
    AMessage.error('缺少主机ID，无法更新')
    return
  }

  try {
    submitLoading.value = true

    // 调用更新主机API
    const response = await updateHostApi(props.hostData.id, formData)

    if (response.code === 200) {
      AMessage.success('主机信息更新成功')

      // 关闭弹窗
      modalVisible.value = false

      // 通知父组件刷新数据
      emit('success')
    } else {
      AMessage.error(response.message || '主机信息更新失败')
    }
  } catch (error) {
    console.error('更新主机失败:', error)
    AMessage.error('主机信息更新失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

/**
 * 处理取消操作
 * 关闭弹窗
 */
const handleCancel = () => {
  modalVisible.value = false
}

/**
 * 弹窗关闭后的回调
 * 确保状态重置
 */
const handleAfterClose = () => {
  submitLoading.value = false
}
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="编辑主机"
    width="600px"
    :mask-closable="false"
    :esc-to-close="false"
    :footer="false"
    @after-close="handleAfterClose"
  >
    <div v-loading="submitLoading">
      <HostForm
        ref="hostFormRef"
        mode="edit"
        :initial-data="hostData"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </div>
  </a-modal>
</template>

<style scoped></style>
