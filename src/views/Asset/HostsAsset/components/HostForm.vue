<script setup lang="ts">
import type { HostRecord, DiskType, DiskInfo } from '@/types/host'

/**
 * 组件属性接口定义
 */
interface Props {
  /** 表单模式：add-新增主机，edit-编辑主机 */
  mode?: 'add' | 'edit'
  /** 初始数据，编辑模式下用于填充表单 */
  initialData?: Partial<HostRecord>
}

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 表单提交事件 - 当用户点击提交按钮且验证通过时触发 */
  submit: [data: HostRecord]
  /** 取消操作事件 - 当用户点击取消按钮时触发 */
  cancel: []
}>()

// 使用默认值设置组件属性
const props = withDefaults(defineProps<Props>(), {
  mode: 'add',
  initialData: () => ({})
})

// 表单引用，用于调用表单验证方法
const formRef = ref()

// 表单数据 - 响应式数据，与表单输入双向绑定
const formData = reactive<HostRecord>({
  hostname: '',
  ipAddress: '',
  cpu: 0,
  memory: 0,
  disks: [{ name: 'sda', size: 0, type: 'ssd' }],
  operatingSystem: '',
  remarks: ''
})

/**
 * 表单验证规则配置
 * 根据业务需求定义各字段的验证规则
 */
const rules = computed(() => ({
  // 主机名称验证：必填，长度限制
  hostname: [
    { required: true, message: '请输入主机名称' },
    { minLength: 2, maxLength: 50, message: '主机名称长度为2-50个字符' }
  ],
  // IP地址验证：必填，格式验证
  ipAddress: [
    { required: true, message: '请输入IP地址' },
    {
      match:
        /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: '请输入正确的IP地址格式'
    }
  ],
  // CPU数量验证：必填，数值范围
  cpu: [
    { required: true, message: '请输入CPU数量' },
    {
      type: 'number',
      min: 1,
      max: 128,
      message: 'CPU数量范围为1-128核'
    }
  ],
  // 内存验证：必填，数值范围
  memory: [
    { required: true, message: '请输入内存大小' },
    {
      type: 'number',
      min: 1,
      max: 9999,
      message: '内存大小范围为1-9999GB'
    }
  ],
  // 硬盘列表验证：必填
  disks: [
    { required: true, message: '请至少添加一个硬盘' },
    {
      type: 'array',
      min: 1,
      message: '请至少添加一个硬盘'
    }
  ],

  // 操作系统验证：必填
  operatingSystem: [
    { required: true, message: '请输入操作系统信息' },
    { minLength: 2, maxLength: 100, message: '操作系统信息长度为2-100个字符' }
  ],

  // 备注信息验证：可选，长度限制
  remarks: [{ maxLength: 500, message: '备注信息不能超过500个字符' }]
}))

/**
 * 初始化表单数据
 * 根据传入的初始数据填充表单，主要用于编辑模式
 */
const initFormData = () => {
  Object.assign(formData, {
    hostname: '',
    ipAddress: '',
    cpu: 0,
    memory: 0,
    disks: [{ name: 'sda', size: 0, type: 'ssd' as DiskType }],
    operatingSystem: '',
    remarks: '',
    ...props.initialData
  })
}

/**
 * 硬盘管理功能
 */
// 添加硬盘
const addDisk = () => {
  const diskName = `sd${String.fromCharCode(97 + formData.disks.length)}` // sda, sdb, sdc...

  formData.disks.push({
    name: diskName,
    size: 0,
    type: 'ssd'
  })
}

// 删除硬盘
const removeDisk = (index: number) => {
  if (formData.disks.length > 1) {
    formData.disks.splice(index, 1)
    // 重新生成硬盘名称以保持连续性
    updateDiskNames()
  }
}

// 更新硬盘名称
const updateDiskNames = () => {
  formData.disks.forEach((disk, index) => {
    disk.name = `sd${String.fromCharCode(97 + index)}` // sda, sdb, sdc...
  })
}

/**
 * 处理表单提交
 * 执行表单验证，验证通过后触发submit事件
 */
const handleSubmit = async () => {
  try {
    // 执行表单验证
    const validateResult = await formRef.value?.validate()
    if (validateResult) {
      // 有验证错误，不提交
      return
    }

    // 表单验证通过，处理提交逻辑
    // 克隆表单数据，避免直接修改原始数据
    const submitData = { ...formData }

    // 触发提交事件，将数据传递给父组件
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

/**
 * 重置表单
 * 清空所有表单数据并重置验证状态
 */
const resetForm = () => {
  formRef.value?.resetFields()
  initFormData()
}

/**
 * 处理取消操作
 * 重置表单并触发cancel事件，通知父组件用户取消操作
 */
const handleCancel = () => {
  resetForm()
  emit('cancel')
}

// 监听初始数据变化，当编辑的主机数据变化时重新初始化表单
watch(() => props.initialData, initFormData, { immediate: true, deep: true })

/**
 * 暴露组件方法供父组件调用
 * resetForm: 重置表单方法
 * validate: 表单验证方法
 */
defineExpose({
  resetForm,
  validate: () => formRef.value?.validate()
})
</script>

<template>
  <!-- 
    主机表单组件 - 封装主机新增和编辑的表单逻辑
    支持新增模式和编辑模式
    集成完整的表单验证和数据处理
  -->
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-col-props="{ span: 6 }"
    :wrapper-col-props="{ span: 18 }"
    label-align="right"
    auto-label-width
  >
    <!-- 主机名称 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="主机名称" field="hostname">
          <a-input
            v-model="formData.hostname"
            placeholder="请输入主机名称"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- IP地址 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="IP地址" field="ipAddress">
          <a-input
            v-model="formData.ipAddress"
            placeholder="请输入IP地址，如：*************"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- CPU数量 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="CPU数量" field="cpu">
          <a-input-number
            v-model="formData.cpu"
            placeholder="请输入CPU数量"
            :min="1"
            :max="128"
            :precision="0"
            class="w-full"
          >
            <template #suffix>核</template>
          </a-input-number>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 内存大小 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="内存大小" field="memory">
          <a-input-number
            v-model="formData.memory"
            placeholder="请输入内存大小"
            :min="1"
            :max="9999"
            :precision="0"
            class="w-full"
          >
            <template #suffix>GB</template>
          </a-input-number>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 硬盘信息 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="硬盘配置" field="disks">
          <div class="space-y-2">
            <div
              v-for="(disk, index) in formData.disks"
              :key="index"
              class="flex items-center gap-2 p-3 border border-gray-200 rounded"
            >
              <div class="flex-1">
                <a-input
                  v-model="disk.name"
                  placeholder="硬盘名称"
                  class="mb-2"
                  :disabled="true"
                />
                <div class="flex gap-2">
                  <a-input-number
                    v-model="disk.size"
                    placeholder="容量"
                    :min="1"
                    :max="99999"
                    :precision="0"
                    class="flex-1"
                  >
                    <template #suffix>GB</template>
                  </a-input-number>
                  <a-select v-model="disk.type" placeholder="类型" class="w-32">
                    <a-option value="ssd">SSD</a-option>
                    <a-option value="hdd">HDD</a-option>
                  </a-select>
                </div>
              </div>
              <a-button
                v-if="formData.disks.length > 1"
                type="text"
                status="danger"
                size="small"
                @click="removeDisk(index)"
              >
                删除
              </a-button>
            </div>
            <a-button
              type="dashed"
              size="small"
              @click="addDisk"
              class="w-full"
            >
              <template #icon>
                <IconRiAddLine />
              </template>
              添加硬盘
            </a-button>
          </div>
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 操作系统 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="操作系统" field="operatingSystem">
          <a-input
            v-model="formData.operatingSystem"
            placeholder="请输入操作系统信息，如：Ubuntu 20.04 LTS"
            allow-clear
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 备注信息 -->
    <a-row>
      <a-col :span="24">
        <a-form-item label="备注信息" field="remarks">
          <a-textarea
            v-model="formData.remarks"
            placeholder="请输入备注信息（可选）"
            :max-length="500"
            show-word-limit
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 操作按钮行 -->
    <a-row>
      <a-col :span="24" class="text-right">
        <a-space>
          <!-- 取消按钮 -->
          <a-button @click="handleCancel">取消</a-button>
          <!-- 提交按钮 - 根据模式显示不同文本 -->
          <a-button type="primary" @click="handleSubmit">
            {{ mode === 'add' ? '新增' : '保存' }}
          </a-button>
        </a-space>
      </a-col>
    </a-row>
  </a-form>
</template>

<style scoped></style>
