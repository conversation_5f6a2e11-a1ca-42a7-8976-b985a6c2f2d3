<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Message as AMessage } from '@arco-design/web-vue'
import type { HostRecord } from '@/types/host'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 主机详情数据
const hostDetail = ref<HostRecord>()

// 加载状态
const loading = ref(false)

/**
 * 获取主机详情数据
 */
const loadHostDetail = async () => {
  const hostId = route.params.id as string
  if (!hostId) {
    AMessage.error('主机ID不存在')
    router.back()
    return
  }

  loading.value = true
  try {
    // 从路由参数中获取主机数据
    const hostDataFromQuery = route.query.hostData as string
    if (!hostDataFromQuery) {
      AMessage.error('主机数据不存在')
      router.back()
      return
    }

    const parsedHostData = JSON.parse(hostDataFromQuery) as HostRecord
    if (parsedHostData.id === hostId) {
      hostDetail.value = parsedHostData
    } else {
      AMessage.error('主机数据不匹配')
      router.back()
    }
  } catch (error) {
    console.error('解析主机数据失败:', error)
    AMessage.error('主机数据格式错误')
    router.back()
  } finally {
    loading.value = false
  }
}

/**
 * 计算硬盘总容量
 */
const totalDiskSize = computed(() => {
  if (!hostDetail.value?.disks) return 0
  return hostDetail.value.disks.reduce((total, disk) => total + disk.size, 0)
})

/**
 * 计算SSD硬盘数量
 */
const ssdCount = computed(() => {
  if (!hostDetail.value?.disks) return 0
  return hostDetail.value.disks.filter(disk => disk.type === 'ssd').length
})

/**
 * 计算HDD硬盘数量
 */
const hddCount = computed(() => {
  if (!hostDetail.value?.disks) return 0
  return hostDetail.value.disks.filter(disk => disk.type === 'hdd').length
})

/**
 * 返回列表页面
 */
const handleBack = () => {
  router.back()
}

/**
 * 编辑主机
 */
const handleEdit = () => {
  AMessage.info('编辑功能开发中...')
}

// 页面加载时获取数据
onMounted(() => {
  loadHostDetail()
})
</script>

<template>
  <div class="host-detail-page">
    <!-- 页面头部 -->
    <div class="mb-4">
      <a-button @click="handleBack">
        <template #icon>
          <IconRiArrowLeftLine />
        </template>
        返回
      </a-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <a-spin size="large" />
    </div>

    <!-- 主机详情内容 -->
    <div v-else-if="hostDetail" class="space-y-6">
      <!-- 基本信息卡片 -->
      <a-card class="rounded-[5px]" :bordered="false">
        <div class="grid grid-cols-2 gap-6">
          <!-- 主机状态 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">主机状态</span>
            <div class="flex items-center gap-2">
              <div
                class="w-2 h-2 rounded-full"
                :class="
                  hostDetail.status === 'online'
                    ? 'bg-green-500'
                    : hostDetail.status === 'offline'
                      ? 'bg-red-500'
                      : 'bg-yellow-500'
                "
              ></div>
              <span
                :class="
                  hostDetail.status === 'online'
                    ? 'text-green-500'
                    : hostDetail.status === 'offline'
                      ? 'text-red-500'
                      : 'text-yellow-500'
                "
              >
                {{
                  hostDetail.status === 'online'
                    ? '在线'
                    : hostDetail.status === 'offline'
                      ? '离线'
                      : '维护中'
                }}
              </span>
            </div>
          </div>

          <!-- IP地址 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">IP地址</span>
            <span class="font-medium">{{ hostDetail.ipAddress }}</span>
          </div>

          <!-- 主机名称 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">主机名称</span>
            <span class="font-medium">{{ hostDetail.hostname }}</span>
          </div>

          <!-- 操作系统 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">操作系统</span>
            <span class="font-medium">{{ hostDetail.operatingSystem }}</span>
          </div>

          <!-- CPU核心数 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">CPU核心数</span>
            <span class="font-medium">{{ hostDetail.cpu }}核</span>
          </div>

          <!-- 内存大小 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">内存大小</span>
            <span class="font-medium">{{ hostDetail.memory }}GB</span>
          </div>

          <!-- 主机类型 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">主机类型</span>
            <span class="font-medium">{{
              hostDetail.type === 'virtual' ? '虚拟机' : '实体机'
            }}</span>
          </div>

          <!-- 主机位置 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">主机位置</span>
            <span class="font-medium">{{ hostDetail.location }}</span>
          </div>

          <!-- 硬盘总容量 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">硬盘总容量</span>
            <span class="font-medium">{{ totalDiskSize }}GB</span>
          </div>

          <!-- 创建时间 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">创建时间</span>
            <span class="font-medium">{{ hostDetail.createTime }}</span>
          </div>

          <!-- 更新时间 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">更新时间</span>
            <span class="font-medium">{{ hostDetail.updateTime }}</span>
          </div>
        </div>
      </a-card>

      <!-- 硬盘信息 -->
      <a-card title="硬盘配置" class="rounded-[5px]" :bordered="false">
        <div class="grid grid-cols-2 gap-6 mb-4">
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">硬盘总数</span>
            <span class="font-medium">{{ hostDetail.disks.length }}块</span>
          </div>
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">SSD数量</span>
            <span class="font-medium">{{ ssdCount }}块</span>
          </div>
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">HDD数量</span>
            <span class="font-medium">{{ hddCount }}块</span>
          </div>
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded">
            <span class="text-gray-600">总容量</span>
            <span class="font-medium">{{ totalDiskSize }}GB</span>
          </div>
        </div>

        <!-- 硬盘详细列表 -->
        <div class="space-y-2">
          <h4 class="text-sm font-medium text-gray-600 mb-3">硬盘详情</h4>
          <div
            v-for="disk in hostDetail.disks"
            :key="disk.name"
            class="flex items-center justify-between p-3 bg-gray-50 rounded"
          >
            <div class="flex items-center gap-3">
              <span class="font-medium">{{ disk.name }}</span>
              <span class="text-sm text-gray-500">{{ disk.size }}GB</span>
            </div>
            <a-tag :color="disk.type === 'ssd' ? 'blue' : 'green'" size="small">
              {{ disk.type.toUpperCase() }}
            </a-tag>
          </div>
        </div>
      </a-card>

      <!-- 备注信息 -->
      <a-card
        v-if="hostDetail.remarks"
        title="备注信息"
        class="rounded-[5px]"
        :bordered="false"
      >
        <div class="p-4 bg-gray-50 rounded">
          <p class="text-gray-700 leading-relaxed">
            {{ hostDetail.remarks }}
          </p>
        </div>
      </a-card>

      <!-- 操作按钮 -->
      <div class="flex justify-center gap-4 pt-4">
        <a-button type="primary" @click="handleEdit">
          <template #icon>
            <IconRiEditLine />
          </template>
          编辑主机
        </a-button>
        <a-button @click="handleBack"> 返回列表 </a-button>
      </div>
    </div>

    <!-- 主机不存在 -->
    <div v-else class="flex justify-center items-center py-20">
      <a-empty description="主机不存在" />
    </div>
  </div>
</template>

<style scoped>
.host-detail-page {
  padding: 20px;
}
</style>
