<script setup lang="ts">
import type { HostRecord } from '@/types/host'

const route = useRoute()
const router = useRouter()

// 主机详情数据
const hostDetail = ref<HostRecord>()

// 加载状态
const loading = ref(false)

/**
 * 获取主机详情数据
 */
const loadHostDetail = async () => {
  const hostId = route.params.id as string
  if (!hostId) {
    AMessage.error('主机ID不存在')
    router.back()
    return
  }

  loading.value = true
  try {
    // 从路由参数中获取主机数据
    const hostDataFromQuery = route.query.hostData as string
    if (!hostDataFromQuery) {
      AMessage.error('主机数据不存在')
      router.back()
      return
    }

    const parsedHostData = JSON.parse(hostDataFromQuery) as HostRecord
    if (parsedHostData.id === hostId) {
      hostDetail.value = parsedHostData
    } else {
      AMessage.error('主机数据不匹配')
      router.back()
    }
  } catch (error) {
    console.error('解析主机数据失败:', error)
    AMessage.error('主机数据格式错误')
    router.back()
  } finally {
    loading.value = false
  }
}

/**
 * 返回列表页面
 */
const handleBack = () => {
  router.back()
}

// 页面加载时获取数据
onMounted(() => {
  loadHostDetail()
})
</script>

<template>
  <!-- 使用 a-card 作为最外层容器 -->
  <a-card class="host-detail-page" :bordered="false">
    <!-- 页面头部 -->
    <template #title>
      <div class="host-title-container">
        <a-space>
          <a-button type="text" shape="circle" @click="handleBack">
            <icon-mdi-arrow-back-circle class="h-25px w-25px" />
          </a-button>
          <span class="font-bold text-lg">{{ hostDetail?.hostname }}</span>
        </a-space>
      </div>
    </template>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <a-spin size="large" />
    </div>

    <!-- 主机详情内容 -->
    <div v-else-if="hostDetail" class="space-y-6">
      <!-- 基本信息 -->
      <div>
        <a-descriptions
          :column="{ xs: 1, sm: 2, md: 3, lg: 4, xl: 5, xxl: 6 }"
          size="medium"
          label-align="left"
          table-layout="fixed"
          layout="inline-vertical"
          :label-style="{
            color: 'var(--color-text-3)'
          }"
          :value-style="{
            color: 'var(--color-text-1)',
            fontWeight: 500,
            paddingBottom: '10px'
          }"
        >
          <a-descriptions-item label="主机状态">
            <IconRiCircleFill
              :class="
                hostDetail.status === 'online'
                  ? 'text-green-500'
                  : hostDetail.status === 'offline'
                    ? 'text-red-500'
                    : 'text-yellow-500'
              "
              class="w-2 h-2 mr-2"
            />

            <span>
              {{
                hostDetail.status === 'online'
                  ? '在线'
                  : hostDetail.status === 'offline'
                    ? '离线'
                    : '维护中'
              }}
            </span>
          </a-descriptions-item>

          <a-descriptions-item label="IP 地址">
            <span>{{ hostDetail.ipAddress }}</span>
          </a-descriptions-item>

          <a-descriptions-item label="操作系统">
            <span>{{ hostDetail.operatingSystem }}</span>
          </a-descriptions-item>

          <a-descriptions-item label="启动时间">
            <span>365 天</span>
          </a-descriptions-item>

          <a-descriptions-item label="架构">
            <span>x86_64</span>
          </a-descriptions-item>

          <a-descriptions-item label="CPU 型号" :span="2">
            <span>Intel(R) Xeon(R) Gold 6248 CPU @ 2.50GHz</span>
          </a-descriptions-item>

          <a-descriptions-item label="CPU">
            <span>{{ hostDetail.cpu }}</span>
          </a-descriptions-item>

          <a-descriptions-item label="内存">
            <span>{{ hostDetail.memory }}</span>
          </a-descriptions-item>

          <a-descriptions-item label="内核版本">
            <span>5.15.0-100-generic</span>
          </a-descriptions-item>

          <a-descriptions-item label="主机位置">
            <span>{{ hostDetail.location }}</span>
          </a-descriptions-item>

          <a-descriptions-item label="最后更新时间">
            <span>2025-08-04 10:00:00</span>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <a-divider orientation="center">
        <a-radio-group size="small" type="button">
          <a-radio value="磁盘信息">磁盘信息</a-radio>
          <a-radio value="网络信息">网络信息</a-radio>
        </a-radio-group>
      </a-divider>
    </div>

    <!-- 主机不存在 -->
    <div v-else class="flex justify-center items-center py-20">
      <a-empty description="主机不存在" />
    </div>
  </a-card>
</template>

<style scoped>
.host-detail-page {
  padding: 20px;
}
</style>
