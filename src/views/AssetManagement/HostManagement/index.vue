<script setup lang="ts">
import type { HostRecord } from '@/types/host'
import { useRouter } from 'vue-router'

const router = useRouter()
// import {
//   getHostListApi,
//   deleteHostApi,
//   type HostListParams
// } from '@/api/host'

/**
 * 搜索表单相关
 */
// 搜索表单引用
const searchFormRef = ref()
// 搜索表单数据
const searchForm = reactive({
  hostname: '', // 主机名称
  ipAddress: '', // IP地址
  operatingSystem: '' // 操作系统
})

/**
 * 模拟数据
 */
const mockHostData: HostRecord[] = [
  {
    id: '1',
    hostname: 'web-server-01',
    ipAddress: '************',
    cpu: 6,
    memory: 16,
    disks: [
      { name: 'sda', size: 256, type: 'ssd' },
      { name: 'sdb', size: 512, type: 'ssd' }
    ],
    operatingSystem: 'Ubuntu 20.04 LTS',
    remarks: 'Web服务器，运行Nginx和Node.js应用',
    createTime: '2023-10-01 10:30:00',
    updateTime: '2024-01-15 14:20:00'
  },
  {
    id: '2',
    hostname: 'db-server-01',
    ipAddress: '************',
    cpu: 8,
    memory: 32,
    disks: [
      { name: 'sda', size: 500, type: 'ssd' },
      { name: 'sdb', size: 1024, type: 'ssd' },
      { name: 'sdc', size: 2048, type: 'hdd' }
    ],
    operatingSystem: 'CentOS 7.9',
    remarks: '数据库服务器，MySQL主节点',
    createTime: '2023-09-15 09:15:00',
    updateTime: '2024-01-10 11:45:00'
  },
  {
    id: '3',
    hostname: 'backup-server-01',
    ipAddress: '************',
    cpu: 8,
    memory: 64,
    disks: [
      { name: 'C盘', size: 256, type: 'ssd' },
      { name: 'D盘', size: 2048, type: 'hdd' },
      { name: 'E盘', size: 4096, type: 'hdd' }
    ],
    operatingSystem: 'Windows Server 2019',
    remarks: '备份服务器，定时备份重要数据',
    createTime: '2023-08-20 16:00:00',
    updateTime: '2023-12-25 08:30:00'
  },
  {
    id: '4',
    hostname: 'app-server-02',
    ipAddress: '************',
    cpu: 6,
    memory: 8,
    disks: [{ name: 'sda', size: 256, type: 'ssd' }],
    operatingSystem: 'Ubuntu 22.04 LTS',
    remarks: '应用服务器，Docker容器运行环境',
    createTime: '2023-11-05 13:20:00',
    updateTime: '2024-01-08 16:10:00'
  },
  {
    id: '5',
    hostname: 'monitoring-server',
    ipAddress: '************',
    cpu: 6,
    memory: 16,
    disks: [
      { name: 'sda', size: 128, type: 'ssd' },
      { name: 'sdb', size: 512, type: 'ssd' }
    ],
    operatingSystem: 'Debian 11',
    remarks: '监控服务器，运行Prometheus和Grafana',
    createTime: '2023-10-10 11:00:00',
    updateTime: '2024-01-12 09:25:00'
  },
  {
    id: '6',
    hostname: 'file-server-01',
    ipAddress: '************',
    cpu: 4,
    memory: 8,
    disks: [
      { name: 'sda', size: 128, type: 'ssd' },
      { name: 'sdb', size: 1024, type: 'hdd' },
      { name: 'sdc', size: 1024, type: 'hdd' },
      { name: 'sdd', size: 2048, type: 'hdd' }
    ],
    operatingSystem: 'FreeNAS 12.0',
    remarks: '文件服务器，NAS存储系统',
    createTime: '2023-09-01 14:30:00',
    updateTime: '2023-12-20 10:15:00'
  },
  {
    id: '7',
    hostname: 'dev-server-01',
    ipAddress: '************',
    cpu: 8,
    memory: 32,
    disks: [
      { name: 'nvme0n1', size: 512, type: 'ssd' },
      { name: 'sda', size: 1024, type: 'ssd' }
    ],
    operatingSystem: 'Arch Linux',
    remarks: '开发服务器，测试环境部署',
    createTime: '2023-11-20 15:45:00',
    updateTime: '2023-12-30 17:00:00'
  },
  {
    id: '8',
    hostname: 'win-server-01',
    ipAddress: '************',
    cpu: 12,
    memory: 64,
    disks: [
      { name: 'C盘', size: 512, type: 'ssd' },
      { name: 'D盘', size: 1024, type: 'ssd' }
    ],
    operatingSystem: 'Windows Server 2022',
    remarks: '企业应用服务器，运行.NET应用',
    createTime: '2023-10-25 12:10:00',
    updateTime: '2024-01-14 13:30:00'
  }
]

/**
 * 表格相关
 */
// 表格数据
const tableData = ref<HostRecord[]>([])
// 原始数据（用于搜索过滤）
const originalData = ref<HostRecord[]>([...mockHostData])
// 加载状态
const loading = ref(false)

// 表格列定义
const columns = [
  {
    title: '主机名称',
    dataIndex: 'hostname',
    width: 150,
    ellipsis: true,
    tooltip: true,
    slotName: 'hostname'
  },
  {
    title: 'IP地址',
    dataIndex: 'ipAddress',
    width: 130,
    ellipsis: true
  },
  {
    title: 'CPU',
    dataIndex: 'cpu',
    width: 80,
    slotName: 'cpu'
  },
  {
    title: '内存',
    dataIndex: 'memory',
    width: 80,
    slotName: 'memory'
  },
  {
    title: '硬盘',
    dataIndex: 'disks',
    width: 200,
    slotName: 'disks'
  },

  {
    title: '操作系统',
    dataIndex: 'operatingSystem',
    width: 180,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    width: 150,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    ellipsis: true
  },
  {
    title: '操作',
    slotName: 'action',
    width: 120,
    fixed: 'right'
  }
]

/**
 * 分页配置
 */
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

/**
 * 搜索功能相关
 */
// 执行搜索
const handleSearch = () => {
  pagination.current = 1
  loadTableData()
}

// 重置搜索条件
const handleReset = () => {
  Object.assign(searchForm, {
    hostname: '',
    ipAddress: '',
    operatingSystem: ''
  })
  pagination.current = 1
  loadTableData()
}

/**
 * 数据加载相关
 */
// 加载表格数据（使用模拟数据）
const loadTableData = async () => {
  loading.value = true

  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  try {
    // 从原始数据开始过滤
    let filteredData = [...originalData.value]

    // 根据搜索条件过滤数据
    if (searchForm.hostname.trim()) {
      const hostname = searchForm.hostname.trim().toLowerCase()
      filteredData = filteredData.filter(item =>
        item.hostname.toLowerCase().includes(hostname)
      )
    }

    if (searchForm.ipAddress.trim()) {
      const ipAddress = searchForm.ipAddress.trim()
      filteredData = filteredData.filter(item =>
        item.ipAddress.includes(ipAddress)
      )
    }

    if (searchForm.operatingSystem.trim()) {
      const os = searchForm.operatingSystem.trim().toLowerCase()
      filteredData = filteredData.filter(item =>
        item.operatingSystem.toLowerCase().includes(os)
      )
    }

    // 设置总数
    pagination.total = filteredData.length

    // 分页处理
    const startIndex = (pagination.current - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    const pagedData = filteredData.slice(startIndex, endIndex)

    // 设置表格数据
    tableData.value = pagedData
  } catch (error) {
    console.error('加载主机数据失败:', error)
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 操作按钮功能
 */
// 新建主机
const handleCreate = () => {
  // 临时提示，后续集成弹窗组件
  AMessage.info('新增主机功能开发中...')
}

// 编辑主机
const handleEdit = () => {
  // 临时提示，后续集成弹窗组件
  AMessage.info('编辑主机功能开发中...')
}

// 查看主机详情
const handleViewDetail = (record: HostRecord) => {
  // 跳转到主机详情页面
  router.push(`/asset/host-detail/${record.id}`)
}

// 刷新数据
const handleRefresh = () => {
  loadTableData()
}

/**
 * 表格操作列功能
 */

// 删除主机
const handleDelete = async (record: HostRecord) => {
  AModal.confirm({
    title: '确认删除',
    content: `确定要删除主机 "${record.hostname}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 200))

        // 从原始数据中删除
        const index = originalData.value.findIndex(
          item => item.id === record.id
        )
        if (index !== -1) {
          originalData.value.splice(index, 1)
        }

        AMessage.success('主机删除成功')

        // 如果当前页没有数据了，回到上一页
        const totalPages = Math.ceil(
          originalData.value.length / pagination.pageSize
        )
        if (pagination.current > totalPages && totalPages > 0) {
          pagination.current = totalPages
        }

        loadTableData()
      } catch (error) {
        AMessage.error('主机删除失败，请重试')
        console.error('删除主机失败:', error)
      }
    }
  })
}

/**
 * 分页事件处理
 */
// 页码变化
const handlePageChange = (page: number) => {
  pagination.current = page
  loadTableData()
}

// 每页条数变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  loadTableData()
}

/**
 * 生命周期
 */
// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<template>
  <a-card class="rounded-[5px]" :bordered="false">
    <!-- 搜索区域 -->
    <a-row>
      <a-col :flex="1">
        <a-form
          ref="searchFormRef"
          :model="searchForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="主机名称" field="hostname">
                <a-input
                  v-model="searchForm.hostname"
                  placeholder="请输入主机名称"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="IP地址" field="ipAddress">
                <a-input
                  v-model="searchForm.ipAddress"
                  placeholder="请输入IP地址"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="操作系统" field="operatingSystem">
                <a-input
                  v-model="searchForm.operatingSystem"
                  placeholder="请输入操作系统"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>

      <a-divider style="height: 84px" direction="vertical" />

      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <IconRiSearchLine />
            </template>
            搜索
          </a-button>
          <a-button @click="handleReset">
            <template #icon>
              <IconRiRefreshLine />
            </template>
            重置
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <a-divider style="margin-top: 0" />

    <!-- 操作按钮区域 -->
    <a-row class="mb-4">
      <a-col :span="12">
        <a-button type="primary" @click="handleCreate">
          <template #icon>
            <IconRiAddLine />
          </template>
          新增
        </a-button>
      </a-col>
      <a-col :span="12" class="flex items-center justify-end">
        <a-tooltip content="刷新">
          <a-button type="text" @click="handleRefresh">
            <icon-tabler-refresh class="text-[var(--color-neutral-10)]" />
          </a-button>
        </a-tooltip>
      </a-col>
    </a-row>

    <!-- 表格区域 -->
    <div>
      <a-table
        row-key="id"
        :columns="columns"
        :data="tableData"
        :loading="{ loading: loading, tip: '加载中...' }"
        :pagination="pagination"
        :bordered="false"
        :scroll="{ x: 1400 }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 空数据状态自定义 -->
        <template #empty>
          <a-empty description="暂无数据" />
        </template>

        <!-- 主机名称列 -->
        <template #hostname="{ record }">
          <a-button
            type="text"
            size="small"
            class="p-0 h-auto text-left"
            @click="handleViewDetail(record)"
          >
            <span
              class="text-[var(--color-primary-6)] hover:text-[var(--color-primary-5)]"
            >
              {{ record.hostname }}
            </span>
          </a-button>
        </template>

        <!-- CPU列 -->
        <template #cpu="{ record }">
          <span>{{ record.cpu }}核</span>
        </template>

        <!-- 内存列 -->
        <template #memory="{ record }">
          <span>{{ record.memory }}GB</span>
        </template>

        <!-- 硬盘列 -->
        <template #disks="{ record }">
          <div class="space-y-1">
            <div
              v-for="disk in record.disks"
              :key="disk.name"
              class="flex items-center gap-1"
            >
              <span class="text-sm">{{ disk.name }}:</span>
              <span class="text-sm">{{ disk.size }}GB</span>
              <a-tag
                :color="disk.type === 'ssd' ? 'blue' : 'green'"
                size="small"
              >
                {{ disk.type.toUpperCase() }}
              </a-tag>
            </div>
          </div>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space size="mini">
            <a-button type="text" size="mini" @click="handleEdit()">
              编辑
            </a-button>
            <a-divider direction="vertical" :margin="0" />
            <a-button
              type="text"
              size="mini"
              status="danger"
              @click="handleDelete(record)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- 新增主机弹窗 -->
    <!-- <AddHost v-model:visible="showAddModal" @success="handleAddSuccess" /> -->

    <!-- 编辑主机弹窗 -->
    <!-- <EditHost
      v-model:visible="showEditModal"
      :host-data="currentEditHost"
      @success="handleEditSuccess"
    /> -->
  </a-card>
</template>

<style scoped></style>
